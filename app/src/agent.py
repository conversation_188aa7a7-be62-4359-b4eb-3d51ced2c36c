"""
Enrollment prediction agent using LangGraph for conversational AI.
Enhanced with human-in-the-loop integration and tool call management.
"""
from langgraph.graph import StateGraph, START, END
from langchain_core.messages import SystemMessage, AIMessage, ToolMessage
from langgraph.graph import add_messages
from typing import Annotated, TypedDict, Literal, List, Dict, Any, Optional
from langchain_groq import ChatGroq
from langchain_community.tools import tool
from langchain_community.tools.tavily_search import TavilySearchResults
from dotenv import load_dotenv
from langgraph.prebuilt import ToolNode
from langgraph.types import Command
import logging
import pandas as pd
import json
import time
from .human_in_loop import human_in_loop_manager
from .tool_schemas import ToolExecutionStatus, create_tool_output

load_dotenv()
logger = logging.getLogger(__name__)


class State(TypedDict):
    """State definition for the agent conversation."""
    messages: Annotated[list, add_messages]
    pending_tool_calls: Dict[str, Any]  # Track pending tool calls
    session_id: str  # Session identifier


class EnrollmentAgent:
    """
    EnrollmentA<PERSON> handles conversational AI for enrollment prediction tasks.
    Uses LangGraph for managing conversation flow and tool usage.
    Enhanced with human-in-the-loop integration.
    """

    def __init__(self, model: ChatGroq, tools: List, memory, system: str = ""):
        """
        Initialize the enrollment prediction agent.

        Args:
            model: ChatGroq model instance
            tools: List of tools available to the agent
            memory: Memory checkpoint for conversation persistence
            system: System prompt for the agent
        """
        self.system = system
        self.model = model.bind_tools(tools)
        self.checkpointer = memory
        self.tools_node = ToolNode(tools)
        self.tools = tools  # Store tools for reference

        # Build the conversation workflow
        workflow = StateGraph(State)
        workflow.add_node("llm", self.call_llm)
        workflow.add_node("tools", self.enhanced_tools_node)
        workflow.add_node("human_approval", self.handle_human_approval)

        workflow.add_edge(START, "llm")
        workflow.add_edge("tools", "llm")
        workflow.add_edge("human_approval", "llm")
        workflow.add_conditional_edges("llm", self.should_continue)

        self.graph = workflow.compile(checkpointer=self.checkpointer)
        logger.info("EnrollmentAgent initialized successfully with human-in-the-loop support")

    def should_continue(self, state: State) -> Literal["tools", "human_approval", "__end__"]:
        """
        Determine whether to continue with tools, require human approval, or end the conversation.

        Args:
            state: Current conversation state

        Returns:
            Next node to execute
        """
        messages = state.get("messages", [])
        if not messages:
            return "__end__"

        last_message = messages[-1]
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            # Check if any pending tool calls require approval
            pending_calls = state.get("pending_tool_calls", {})
            if pending_calls:
                return "human_approval"
            return "tools"
        return "__end__"

    def call_llm(self, state: State) -> Command[Literal["tools", "__end__"]]:
        """
        Call the language model with the current conversation state.
        
        Args:
            state: Current conversation state
            
        Returns:
            Command with the next action and updated state
        """
        try:
            messages = [SystemMessage(content=self.system)] + state["messages"]
            response = self.model.invoke(messages)
            
            if hasattr(response, 'tool_calls') and len(response.tool_calls) > 0:
                next_node = "tools"
            else:
                next_node = "__end__"
            
            return Command(goto=next_node, update={"messages": response})
            
        except Exception as e:
            logger.error(f"Error in call_llm: {str(e)}", exc_info=True)
            error_message = AIMessage(content="I apologize, but I encountered an error while processing your request. Please try again.")
            return Command(goto="__end__", update={"messages": error_message})

    async def enhanced_tools_node(self, state: State) -> Command[Literal["__end__"]]:
        """
        Enhanced tool execution with human-in-the-loop integration.

        Args:
            state: Current conversation state

        Returns:
            Command with updated state
        """
        try:
            messages = state.get("messages", [])
            session_id = state.get("session_id", "default")

            if not messages:
                return Command(goto="__end__", update={})

            last_message = messages[-1]
            if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
                return Command(goto="__end__", update={})

            tool_responses = []
            pending_calls = {}

            for tool_call in last_message.tool_calls:
                tool_name = tool_call["name"]
                tool_args = tool_call["args"]
                call_id = tool_call["id"]

                # Find the original user message for context
                user_message = ""
                for msg in reversed(messages[:-1]):  # Exclude the AI message with tool calls
                    if hasattr(msg, 'content') and msg.content:
                        user_message = msg.content
                        break

                # Create execution callback
                async def execute_tool(approved: bool) -> Dict[str, Any]:
                    if not approved:
                        return create_tool_output(
                            success=False,
                            error_message="Tool execution was not approved by user"
                        ).dict()

                    # Execute the actual tool
                    start_time = time.time()
                    try:
                        # Find and execute the tool
                        for tool in self.tools:
                            if tool.name == tool_name:
                                result = tool.invoke(tool_args)
                                execution_time = time.time() - start_time
                                return create_tool_output(
                                    success=True,
                                    result=result,
                                    execution_time=execution_time
                                ).dict()

                        return create_tool_output(
                            success=False,
                            error_message=f"Tool '{tool_name}' not found"
                        ).dict()

                    except Exception as e:
                        execution_time = time.time() - start_time
                        logger.error(f"Tool execution error: {e}")
                        return create_tool_output(
                            success=False,
                            error_message=str(e),
                            execution_time=execution_time
                        ).dict()

                # Request tool execution through human-in-loop manager
                response = await human_in_loop_manager.request_tool_execution(
                    tool_name=tool_name,
                    parameters=tool_args,
                    session_id=session_id,
                    user_message=user_message,
                    execute_callback=execute_tool
                )

                if response.status == ToolExecutionStatus.PENDING:
                    # Store pending call info
                    pending_calls[response.request_id] = {
                        "tool_call_id": call_id,
                        "tool_name": tool_name,
                        "parameters": tool_args,
                        "confirmation_message": response.confirmation_message,
                        "risk_level": response.risk_level.value
                    }

                    # Create a message indicating approval is needed
                    approval_message = f"🔄 **Tool Execution Pending Approval**\n\n{response.confirmation_message}"
                    tool_responses.append(ToolMessage(
                        content=approval_message,
                        tool_call_id=call_id
                    ))
                else:
                    # Tool was executed immediately
                    if response.result and response.result.success:
                        content = response.result.result
                    else:
                        content = response.result.error_message if response.result else "Tool execution failed"

                    tool_responses.append(ToolMessage(
                        content=content,
                        tool_call_id=call_id
                    ))

            # Update state with tool responses and pending calls
            update_dict = {"messages": tool_responses}
            if pending_calls:
                update_dict["pending_tool_calls"] = pending_calls

            return Command(goto="__end__", update=update_dict)

        except Exception as e:
            logger.error(f"Error in enhanced_tools_node: {str(e)}", exc_info=True)
            error_message = ToolMessage(
                content=f"I encountered an error while processing the tool request: {str(e)}",
                tool_call_id="error"
            )
            return Command(goto="__end__", update={"messages": [error_message]})

    async def handle_human_approval(self, state: State) -> Command[Literal["__end__"]]:
        """
        Handle human approval for pending tool calls.
        This method waits for external approval/rejection.

        Args:
            state: Current conversation state

        Returns:
            Command with updated state
        """
        try:
            pending_calls = state.get("pending_tool_calls", {})
            if not pending_calls:
                return Command(goto="__end__", update={})

            # In a real implementation, this would wait for user input
            # For now, we'll return a message asking for approval
            approval_messages = []

            for request_id, call_info in pending_calls.items():
                message_content = f"""
🔄 **Waiting for your approval to execute:**

**Tool:** {call_info['tool_name']}
**Risk Level:** {call_info['risk_level'].title()}
**Request ID:** {request_id}

{call_info['confirmation_message']}

*Please use the approval interface to approve or reject this tool execution.*
"""
                approval_messages.append(AIMessage(content=message_content))

            return Command(goto="__end__", update={"messages": approval_messages})

        except Exception as e:
            logger.error(f"Error in handle_human_approval: {str(e)}", exc_info=True)
            error_message = AIMessage(
                content="I encountered an error while processing the approval request. Please try again."
            )
            return Command(goto="__end__", update={"messages": [error_message]})

    def get_pending_tool_calls(self, session_id: str) -> Dict[str, Any]:
        """Get pending tool calls for a session."""
        return human_in_loop_manager.get_pending_calls(session_id)

    async def approve_tool_call(self, request_id: str) -> Dict[str, Any]:
        """Approve a pending tool call."""
        return await human_in_loop_manager.approve_tool_call(request_id)

    async def reject_tool_call(self, request_id: str, reason: str = "User rejected") -> None:
        """Reject a pending tool call."""
        await human_in_loop_manager.reject_tool_call(request_id, reason)

