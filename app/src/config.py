"""
Configuration settings for the EnrollPredict.ai application.
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Base directory paths
BASE_DIR = Path(__file__).resolve().parent.parent
SRC_DIR = BASE_DIR / "src"
STATIC_DIR = BASE_DIR / "static"
TEMPLATES_DIR = BASE_DIR / "templates"
MODEL_DIR = SRC_DIR / "model"
PREDICT_MODEL_DIR = SRC_DIR / "predict_model"

# API Keys and Model Configuration
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")

# Model Configuration
DEFAULT_MODEL = "llama-3.3-70b-versatile"
DEFAULT_TEMPERATURE = 0

# System Messages
SYSTEM_PROMPT = """You are an AI assistant specialized in university enrollment prediction and analysis. 
You have access to advanced prediction tools that can help forecast enrollment numbers based on various factors.

Your capabilities include:
- Predicting university enrollment for specific programs and years
- Analyzing enrollment trends and patterns
- Providing insights on factors affecting enrollment
- Helping with educational planning and resource allocation

When users ask about enrollment predictions, use the available tools to provide accurate and detailed forecasts.
Always explain your predictions and the factors that influence them."""

# File Paths for Models
ADMISSION_CNN_MODEL_PATH = MODEL_DIR / "admission_cnn_model.keras"
ADMISSION_LSTM_MODEL_PATH = MODEL_DIR / "admission_lstm_model.keras"
ADMISSION_SCALER_PATH = MODEL_DIR / "admission_scaler.pkl"
TARGET_SCALER_PATH = MODEL_DIR / "target_scaler.pkl"
REGRESSOR_PATH = MODEL_DIR / "admission_prediction_model_regressor.pkl"

# Enrollment Prediction Model Paths
ENROLLMENT_MODEL_PATH = PREDICT_MODEL_DIR / "enrollment_predictor_final.keras"
SCALER_X_PATH = PREDICT_MODEL_DIR / "scaler_x_final.joblib"
SCALER_Y_PATH = PREDICT_MODEL_DIR / "scaler_y_final.joblib"

# Application Settings
DEBUG = os.getenv("DEBUG", "False").lower() == "true"
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", "8000"))
