"""
Chat service module for handling conversations with the AI agent.
Enhanced with human-in-the-loop support and tool call management.
"""
import logging
from typing import Dict, Any, Optional
from langchain_core.messages import HumanMessage, AIMessage
from .agent import EnrollmentAgent
from .config import SYSTEM_PROMPT
from .human_in_loop import human_in_loop_manager

logger = logging.getLogger(__name__)


class ChatService:
    """Service class for managing chat interactions with the AI agent."""
    
    def __init__(self, agent: EnrollmentAgent):
        """
        Initialize the chat service with an agent.
        
        Args:
            agent: The EnrollmentAgent instance to use for processing messages
        """
        self.agent = agent
        self.session_threads = {}  # Store thread IDs for different sessions
    
    def get_or_create_thread_id(self, session_id: str = "default") -> str:
        """
        Get or create a thread ID for a session.
        
        Args:
            session_id: Unique identifier for the chat session
        
        Returns:
            Thread ID for the session
        """
        if session_id not in self.session_threads:
            self.session_threads[session_id] = f"thread_{session_id}_{len(self.session_threads)}"
        return self.session_threads[session_id]
    
    async def process_message(self, message: str, session_id: str = "default") -> Dict[str, Any]:
        """
        Process a user message and return the agent's response.
        
        Args:
            message: The user's message
            session_id: Session identifier for conversation context
        
        Returns:
            Dictionary containing the response and metadata
        """
        try:
            # Validate input
            if not message or not message.strip():
                return {
                    "response": "Please provide a message for me to respond to.",
                    "error": None,
                    "success": True
                }
            
            # Create human message
            human_message = HumanMessage(content=message.strip())
            thread_id = self.get_or_create_thread_id(session_id)
            
            logger.info(f"Processing message for session {session_id}: {message[:100]}...")
            
            # Process through agent with session context (using async invoke)
            result = await self.agent.graph.ainvoke(
                {
                    "messages": [human_message],
                    "session_id": session_id,
                    "pending_tool_calls": {}
                },
                config={"configurable": {"thread_id": thread_id}}
            )
            
            # Extract response
            if result and "messages" in result and result["messages"]:
                final_message = result["messages"][-1]
                response_content = final_message.content if hasattr(final_message, 'content') else str(final_message)
            else:
                response_content = "I apologize, but I couldn't process your request. Please try rephrasing your question."
            
            logger.info(f"Successfully processed message for session {session_id}")
            
            return {
                "response": response_content,
                "error": None,
                "success": True,
                "session_id": session_id,
                "thread_id": thread_id
            }
            
        except Exception as e:
            error_msg = f"Error processing message: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            return {
                "response": "I'm sorry, I encountered an error while processing your request. Please try again or rephrase your question.",
                "error": error_msg,
                "success": False,
                "session_id": session_id
            }
    
    def clear_session(self, session_id: str) -> bool:
        """
        Clear a chat session.
        
        Args:
            session_id: Session identifier to clear
        
        Returns:
            True if session was cleared, False if it didn't exist
        """
        if session_id in self.session_threads:
            del self.session_threads[session_id]
            logger.info(f"Cleared session {session_id}")
            return True
        return False

    def get_pending_tool_calls(self, session_id: str) -> Dict[str, Any]:
        """
        Get pending tool calls for a session.

        Args:
            session_id: Session identifier

        Returns:
            Dictionary of pending tool calls
        """
        return self.agent.get_pending_tool_calls(session_id)

    async def approve_tool_call(self, request_id: str, session_id: str) -> Dict[str, Any]:
        """
        Approve a pending tool call.

        Args:
            request_id: Tool call request ID
            session_id: Session identifier

        Returns:
            Result of tool execution
        """
        try:
            result = await self.agent.approve_tool_call(request_id)
            logger.info(f"Tool call {request_id} approved for session {session_id}")
            return {
                "success": True,
                "result": result,
                "message": "Tool call approved and executed successfully"
            }
        except Exception as e:
            logger.error(f"Error approving tool call {request_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to approve tool call"
            }

    async def reject_tool_call(self, request_id: str, session_id: str, reason: str = "User rejected") -> Dict[str, Any]:
        """
        Reject a pending tool call.

        Args:
            request_id: Tool call request ID
            session_id: Session identifier
            reason: Reason for rejection

        Returns:
            Confirmation of rejection
        """
        try:
            await self.agent.reject_tool_call(request_id, reason)
            logger.info(f"Tool call {request_id} rejected for session {session_id}: {reason}")
            return {
                "success": True,
                "message": "Tool call rejected successfully"
            }
        except Exception as e:
            logger.error(f"Error rejecting tool call {request_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to reject tool call"
            }
    
    def get_active_sessions(self) -> list:
        """
        Get list of active session IDs.
        
        Returns:
            List of active session IDs
        """
        return list(self.session_threads.keys())
