"""
Enhanced tool schema system with comprehensive validation and human-in-the-loop support.
"""
from typing import Dict, Any, Optional, List, Literal
from pydantic import BaseModel, Field, validator
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ToolRiskLevel(str, Enum):
    """Risk levels for tool execution."""
    SAFE = "safe"           # Auto-execute without confirmation
    MODERATE = "moderate"   # Require user confirmation
    HIGH = "high"          # Require explicit user approval with warnings


class ToolExecutionStatus(str, Enum):
    """Status of tool execution."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"


class ToolMetadata(BaseModel):
    """Metadata for tool configuration and behavior."""
    name: str
    description: str
    risk_level: ToolRiskLevel
    requires_confirmation: bool = False
    confirmation_message: Optional[str] = None
    category: str = "general"
    tags: List[str] = []
    
    class Config:
        use_enum_values = True


class EnrollmentPredictionInput(BaseModel):
    """Input schema for enrollment prediction tool."""
    year: int = Field(
        ..., 
        ge=2020, 
        le=2050, 
        description="Target year for prediction (2020-2050)"
    )
    previous_year_enrollment: int = Field(
        ..., 
        ge=0, 
        le=100000, 
        description="Actual enrollment from previous year (0-100,000)"
    )
    applications_received: int = Field(
        ..., 
        ge=0, 
        le=1000000, 
        description="Total applications received (0-1,000,000)"
    )
    acceptance_rate: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Proportion of applicants offered admission (0.0-1.0)"
    )
    conversion_rate: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Proportion of accepted students who enroll (0.0-1.0)"
    )
    secondary_school_graduates: int = Field(
        ..., 
        ge=0, 
        le=10000000, 
        description="Number of secondary school graduates (0-10,000,000)"
    )
    scholarship_funds_available: float = Field(
        ..., 
        ge=0.0, 
        description="Total scholarship funds available (USD)"
    )
    average_entry_test_score: float = Field(
        ..., 
        ge=0.0, 
        le=1600.0, 
        description="Average standardized test score (0-1600)"
    )
    female_applicant_percentage: float = Field(
        ..., 
        ge=0.0, 
        le=100.0, 
        description="Percentage of female applicants (0-100)"
    )
    urban_applicant_percentage: float = Field(
        ..., 
        ge=0.0, 
        le=100.0, 
        description="Percentage of urban applicants (0-100)"
    )
    international_applicant_percentage: float = Field(
        ..., 
        ge=0.0, 
        le=100.0, 
        description="Percentage of international applicants (0-100)"
    )
    faculty_student_ratio: float = Field(
        ..., 
        gt=0.0, 
        le=1.0, 
        description="Faculty to student ratio (0.0-1.0)"
    )

    @validator('acceptance_rate', 'conversion_rate')
    def validate_rates(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('Rate must be between 0.0 and 1.0')
        return v

    @validator('female_applicant_percentage', 'urban_applicant_percentage', 'international_applicant_percentage')
    def validate_percentages(cls, v):
        if not 0.0 <= v <= 100.0:
            raise ValueError('Percentage must be between 0 and 100')
        return v


class AdmissionPredictionInput(BaseModel):
    """Input schema for admission chance prediction tool."""
    gre_score: float = Field(
        ..., 
        ge=130.0, 
        le=170.0, 
        description="GRE score (130-170)"
    )
    toefl_score: float = Field(
        ..., 
        ge=0.0, 
        le=120.0, 
        description="TOEFL score (0-120)"
    )
    sop: float = Field(
        ..., 
        ge=1.0, 
        le=5.0, 
        description="Statement of Purpose score (1-5)"
    )
    lor: float = Field(
        ..., 
        ge=1.0, 
        le=5.0, 
        description="Letter of Recommendation score (1-5)"
    )
    cgpa: float = Field(
        ..., 
        ge=0.0, 
        le=10.0, 
        description="CGPA (0-10)"
    )

    @validator('gre_score')
    def validate_gre(cls, v):
        if not 130.0 <= v <= 170.0:
            raise ValueError('GRE score must be between 130 and 170')
        return v

    @validator('toefl_score')
    def validate_toefl(cls, v):
        if not 0.0 <= v <= 120.0:
            raise ValueError('TOEFL score must be between 0 and 120')
        return v

    @validator('sop', 'lor')
    def validate_scores(cls, v):
        if not 1.0 <= v <= 5.0:
            raise ValueError('Score must be between 1 and 5')
        return v

    @validator('cgpa')
    def validate_cgpa(cls, v):
        if not 0.0 <= v <= 10.0:
            raise ValueError('CGPA must be between 0 and 10')
        return v


class ToolOutput(BaseModel):
    """Standardized tool output schema."""
    success: bool
    result: Any
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = {}
    execution_time: Optional[float] = None


class ToolCallRequest(BaseModel):
    """Schema for tool call requests."""
    tool_name: str
    parameters: Dict[str, Any]
    session_id: str
    request_id: str
    user_message: str


class ToolCallResponse(BaseModel):
    """Schema for tool call responses."""
    request_id: str
    tool_name: str
    status: ToolExecutionStatus
    parameters: Dict[str, Any]
    result: Optional[ToolOutput] = None
    requires_confirmation: bool = False
    confirmation_message: Optional[str] = None
    risk_level: ToolRiskLevel
    metadata: Dict[str, Any] = {}


# Tool registry with metadata
TOOL_REGISTRY: Dict[str, ToolMetadata] = {
    "predict_enrollment": ToolMetadata(
        name="predict_enrollment",
        description="Predicts university enrollment based on institutional and demographic factors",
        risk_level=ToolRiskLevel.SAFE,
        requires_confirmation=False,
        category="prediction",
        tags=["enrollment", "prediction", "education"]
    ),
    "predict_admission_chance": ToolMetadata(
        name="predict_admission_chance",
        description="Predicts admission chances for graduate program applicants",
        risk_level=ToolRiskLevel.SAFE,
        requires_confirmation=False,
        category="prediction",
        tags=["admission", "prediction", "graduate"]
    )
}


def get_tool_metadata(tool_name: str) -> Optional[ToolMetadata]:
    """Get metadata for a specific tool."""
    return TOOL_REGISTRY.get(tool_name)


def validate_tool_input(tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate tool input parameters against schema.
    
    Args:
        tool_name: Name of the tool
        parameters: Input parameters to validate
        
    Returns:
        Validated parameters dictionary
        
    Raises:
        ValueError: If validation fails
    """
    try:
        if tool_name == "predict_enrollment":
            validated = EnrollmentPredictionInput(**parameters)
            return validated.dict()
        elif tool_name == "predict_admission_chance":
            validated = AdmissionPredictionInput(**parameters)
            return validated.dict()
        else:
            logger.warning(f"No validation schema found for tool: {tool_name}")
            return parameters
    except Exception as e:
        logger.error(f"Validation failed for tool {tool_name}: {str(e)}")
        raise ValueError(f"Invalid parameters for {tool_name}: {str(e)}")


def create_tool_output(success: bool, result: Any = None, error_message: str = None, 
                      metadata: Dict[str, Any] = None, execution_time: float = None) -> ToolOutput:
    """Create a standardized tool output."""
    return ToolOutput(
        success=success,
        result=result,
        error_message=error_message,
        metadata=metadata or {},
        execution_time=execution_time
    )
