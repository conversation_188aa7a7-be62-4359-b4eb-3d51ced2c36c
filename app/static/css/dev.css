@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&family=Open+Sans:wght@400;600&family=Nunito:wght@400;700&family=Roboto:wght@400;500&display=swap');

body {
    font-family: 'Open Sans', sans-serif;
    text-align: center;
    background-color: #f8f8f8;
    margin: 0;
    padding: 0;
}
.container {
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
h1 {
    font-size: 28px;
    font-weight: bold;
}
.developer {
    display: flex;
    align-items: center;
    margin: 20px 0;
    padding: 10px;
    background: #f0f0f0;
    border-radius: 8px;
}
.developer img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-right: 20px;
}
.info {
    text-align: left;
    flex: 1;
}
.info h2 {
    margin: 5px 0;
}
.linkedin-icon {
    
    font-size: 36px;
}

footer {
    margin-top: 40px;
    padding: 15px;
    background: #f8f8f8;
    font-size: 14px;
}

/* --- RESPONSIVE DESIGN --- */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        text-align: center;
    }

    nav ul {
        flex-direction: column;
        gap: 10px;
        padding-top: 10px;
    }

    .buttons {
        flex-direction: column;
        align-items: center;
    }

    .buttons button {
        width: 80%;
        max-width: 250px;
    }

    .chat-bubble {
        max-width: 90%;
    }

    main {
        padding: 20px;
    }

    h1 {
        font-size: 22px;
    }

    p {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .logo {
        font-size: 18px;
    }

    nav ul {
        flex-direction: column;
        padding-top: 10px;
    }

    nav ul li a {
        font-size: 14px;
    }

    .github-icon i {
        font-size: 20px;
    }

    h1 {
        font-size: 20px;
    }

    p {
        font-size: 14px;
    }

    .buttons button {
        font-size: 14px;
        padding: 10px 15px;
    }

    footer {
        font-size: 12px;
    }
}

