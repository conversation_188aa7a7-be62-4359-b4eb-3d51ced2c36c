/* ===== TOOL CALL VISUALIZATION STYLES ===== */

/* Tool Call Container */
.tool-call-container {
    position: fixed;
    top: 50%;
    right: -400px; /* Hidden by default */
    width: 380px;
    max-height: 80vh;
    background: var(--bg-primary);
    border: 1px solid var(--ai-message-border);
    border-radius: var(--radius-lg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform: translateY(-50%);
    transition: right 0.3s ease-out;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.tool-call-container.visible {
    right: 20px;
}

/* Tool Call Header */
.tool-call-container .tool-call-header {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    padding: var(--space-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--ai-message-border);
}

.tool-call-container .tool-call-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.tool-call-close {
    background: transparent;
    border: none;
    color: var(--text-inverse);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

.tool-call-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Tool Call List */
.tool-call-list {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-sm);
    max-height: calc(80vh - 60px);
}

.no-pending-calls {
    text-align: center;
    color: var(--text-muted);
    padding: var(--space-xl);
    font-style: italic;
}

/* Individual Tool Call Item */
.tool-call-item {
    background: var(--bg-secondary);
    border: 1px solid var(--ai-message-border);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-md);
    overflow: hidden;
    transition: all var(--transition-fast);
}

.tool-call-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(51, 70, 255, 0.1);
}

.tool-call-item:last-child {
    margin-bottom: 0;
}

/* Tool Call Item Header */
.tool-call-item .tool-call-header {
    background: var(--bg-tertiary);
    padding: var(--space-md);
    border-bottom: 1px solid var(--ai-message-border);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--space-md);
}

.tool-info {
    flex: 1;
}

.tool-name {
    margin: 0 0 var(--space-xs) 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.tool-name i {
    color: var(--primary-color);
}

/* Risk Level Badges */
.risk-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.risk-safe {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.risk-moderate {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.risk-high {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.risk-unknown {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

/* Tool Actions */
.tool-actions {
    display: flex;
    gap: var(--space-xs);
    flex-shrink: 0;
}

.btn-approve,
.btn-reject {
    padding: var(--space-xs) var(--space-sm);
    border: none;
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.btn-approve {
    background: #10b981;
    color: white;
}

.btn-approve:hover {
    background: #059669;
    transform: translateY(-1px);
}

.btn-reject {
    background: #ef4444;
    color: white;
}

.btn-reject:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* Tool Details */
.tool-details {
    padding: var(--space-md);
}

.confirmation-message {
    background: var(--bg-primary);
    border: 1px solid var(--ai-message-border);
    border-radius: var(--radius-sm);
    padding: var(--space-md);
    margin-bottom: var(--space-md);
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-secondary);
}

.confirmation-message strong {
    color: var(--text-primary);
}

/* Parameters Section */
.parameters-section {
    margin-bottom: var(--space-md);
}

.parameters-section h5 {
    margin: 0 0 var(--space-sm) 0;
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.parameters-section h5 i {
    color: var(--primary-color);
}

.parameters-list {
    list-style: none;
    padding: 0;
    margin: 0;
    background: var(--bg-primary);
    border: 1px solid var(--ai-message-border);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.parameters-list li {
    padding: var(--space-sm);
    border-bottom: 1px solid var(--ai-message-border);
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.parameters-list li:last-child {
    border-bottom: none;
}

.parameters-list li strong {
    color: var(--text-primary);
    font-weight: 500;
}

.parameters-list li em {
    color: var(--text-muted);
    font-style: italic;
}

/* Tool Metadata */
.tool-metadata {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-sm);
    border-top: 1px solid var(--ai-message-border);
    font-size: 0.75rem;
    color: var(--text-muted);
}

.request-id,
.created-time {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tool-call-container {
        right: -100vw;
        width: 100vw;
        height: 100vh;
        top: 0;
        transform: none;
        border-radius: 0;
        max-height: none;
    }

    .tool-call-container.visible {
        right: 0;
    }

    .tool-call-item .tool-call-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-sm);
    }

    .tool-actions {
        justify-content: stretch;
    }

    .btn-approve,
    .btn-reject {
        flex: 1;
        justify-content: center;
        padding: var(--space-sm);
    }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
    .tool-call-container {
        background: var(--bg-secondary);
        border-color: var(--ai-message-border);
    }

    .tool-call-item {
        background: var(--bg-primary);
        border-color: var(--ai-message-border);
    }

    .tool-call-item .tool-call-header {
        background: var(--bg-tertiary);
    }

    .confirmation-message,
    .parameters-list {
        background: var(--bg-secondary);
        border-color: var(--ai-message-border);
    }

    .parameters-list li {
        border-color: var(--ai-message-border);
    }

    .tool-metadata {
        border-color: var(--ai-message-border);
    }
}

/* Animation for new tool calls */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.tool-call-item {
    animation: slideInRight 0.3s ease-out;
}

/* Loading states */
.btn-approve:disabled,
.btn-reject:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-approve:disabled:hover,
.btn-reject:disabled:hover {
    transform: none !important;
}
