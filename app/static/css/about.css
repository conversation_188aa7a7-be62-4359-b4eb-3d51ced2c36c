/* Import Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&family=Open+Sans:wght@400;600&family=Nunito:wght@400;700&family=Roboto:wght@400;500&display=swap');

/* General Page Styles */
body {
    font-family: 'Open Sans', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

/* Header Section */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f8f8;
    flex-wrap: wrap;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 20px;
    padding: 0;
}

nav ul li {
    display: inline;
}

nav ul li a {
    text-decoration: none;
    color: black;
    font-size: 16px;
}

/* GitHub Icon */
.github-icon a {
    text-decoration: none;
    color: inherit;
}

.github-icon i {
    font-size: 24px;
    color: black;
}

.buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.buttons button {
    margin: 10px;
    padding: 12px 20px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    border-radius: 5px;
}

/* Buttons */
.buttons button {
    margin: 15px;
    padding: 12px 20px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    border-radius: 50px; /* Makes button round */
    font-family: 'Nunito', sans-serif;
}

header h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 36px;
    margin-bottom: 10px;
}

header p {
    font-size: 18px;
}

/* About Us Section */
#about-us {
    padding: 40px 20px;
    background-color: white;
    margin-top: 20px;
}

#about-us h2 {
    font-family: 'Nunito', sans-serif;
    font-size: 32px;
    color: #333;
    margin-bottom: 20px;
}

#about-us p {
    font-size: 18px;
    color: #555;
    line-height: 1.6;
}

/* Our Work Section */
#our-work {
    padding: 40px 20px;
    background-color: #fafafa;
}

#our-work h2 {
    font-family: 'Nunito', sans-serif;
    font-size: 32px;
    color: #333;
    margin-bottom: 20px;
}

#our-work p {
    font-size: 18px;
    color: #555;
    line-height: 1.6;
}

#our-work ul {
    list-style: none;
    padding: 0;
    font-size: 18px;
    color: #555;
}

#our-work ul li {
    margin-bottom: 10px;
}

/* Gallery Section */
#gallery {
    padding: 40px 20px;
    background-color: white;
    text-align: center;
}

#gallery h2 {
    font-family: 'Nunito', sans-serif;
    font-size: 32px;
    color: #333;
    margin-bottom: 20px;
}

.gallery-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.gallery-container img {
    width: 280px;
    height: 180px;
    object-fit: cover;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.gallery-container img:hover {
    transform: scale(1.05);
}

/* About Us Section */
#about-us {
    padding: 40px 20px;
    background-color: white;
    margin-top: 20px;
}

#about-us h2 {
    font-family: 'Nunito', sans-serif;
    font-size: 32px;
    color: #333;
    margin-bottom: 20px;
}

#about-us p {
    font-size: 18px;
    color: #555;
    line-height: 1.6;
}

/* About Us Section */
#about-us img {
    width: 100%;
    max-width: 600px;
    height: auto;
    border-radius: 8px;
    margin-top: 20px;
    margin-left: auto;
    margin-right: auto;
    display: block;
    transition: transform 0.3s ease;
}

#about-us img:hover {
    transform: scale(1.05);
}




/* Our Work Section */
#our-work {
    padding: 40px 20px;
    background-color: #fafafa;
}

#our-work h2 {
    font-family: 'Nunito', sans-serif;
    font-size: 32px;
    color: #333;
    margin-bottom: 20px;
}

#our-work p {
    font-size: 18px;
    color: #555;
    line-height: 1.6;
}

#our-work ul {
    list-style: none;
    padding: 0;
    font-size: 18px;
    color: #555;
}

#our-work ul li {
    margin-bottom: 10px;
}

#our-work img {
    width: 100%;
    max-width: 300px;
    height: auto;
    border-radius: 8px;
    margin-top: 10px;
    transition: transform 0.3s ease;
}

#our-work img:hover {
    transform: scale(1.05);
}


/* Contact Section */
#contact {
    padding: 40px 20px;
    background-color: #f0f0f0;
    text-align: center;
}

#contact h2 {
    font-family: 'Nunito', sans-serif;
    font-size: 32px;
    color: #333;
    margin-bottom: 20px;
}

#contact p {
    font-size: 18px;
    color: #555;
}

#contact a {
    text-decoration: none;
    color: #3346FF;
    font-weight: bold;
}

/* Footer */
footer {
    padding: 20px;
    background-color: #3346FF;
    color: white;
    text-align: center;
}

footer p {
    font-size: 14px;
}

/* --- RESPONSIVE DESIGN --- */
@media (max-width: 768px) {
    header {
        padding: 20px 15px;
    }

    #about-us, #our-work, #gallery, #contact {
        padding: 30px 15px;
    }

    .gallery-container {
        flex-direction: column;
    }

    .gallery-container img {
        width: 100%;
        margin-bottom: 15px;
    }

    h1 {
        font-size: 28px;
    }

    h2 {
        font-size: 28px;
    }

    p {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 24px;
    }

    #about-us h2, #our-work h2, #gallery h2, #contact h2 {
        font-size: 24px;
    }

    footer p {
        font-size: 12px;
    }

    p {
        font-size: 14px;
    }
}
