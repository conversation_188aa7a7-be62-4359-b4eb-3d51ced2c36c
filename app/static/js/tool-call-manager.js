/**
 * Tool Call Manager - Handles tool call visualization and user approval/rejection
 * Part of the enhanced AI agent system with human-in-the-loop integration
 */

class ToolCallManager {
    constructor() {
        this.pendingCalls = new Map();
        this.sessionId = "default";
        this.pollInterval = null;
        this.init();
    }

    init() {
        this.createToolCallContainer();
        this.startPolling();
        this.setupEventListeners();
    }

    createToolCallContainer() {
        // Check if container already exists
        if (document.getElementById('tool-call-container')) {
            return;
        }

        const container = document.createElement('div');
        container.id = 'tool-call-container';
        container.className = 'tool-call-container';
        container.innerHTML = `
            <div class="tool-call-header">
                <h3><i class="fas fa-tools"></i> Tool Execution Requests</h3>
                <button class="tool-call-close" onclick="toolCallManager.hideContainer()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="tool-call-list" id="tool-call-list">
                <!-- Tool calls will be populated here -->
            </div>
        `;

        // Add to page
        document.body.appendChild(container);
    }

    setupEventListeners() {
        // Listen for escape key to close container
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideContainer();
            }
        });
    }

    setSessionId(sessionId) {
        this.sessionId = sessionId;
    }

    startPolling() {
        // Poll for pending tool calls every 2 seconds
        this.pollInterval = setInterval(() => {
            this.fetchPendingCalls();
        }, 2000);
    }

    stopPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
    }

    async fetchPendingCalls() {
        try {
            const response = await fetch(`/api/tool-calls/${this.sessionId}`);
            const data = await response.json();

            if (data.success && data.pending_calls) {
                this.updatePendingCalls(data.pending_calls);
            }
        } catch (error) {
            console.error('Error fetching pending tool calls:', error);
        }
    }

    updatePendingCalls(pendingCalls) {
        const hasNewCalls = Object.keys(pendingCalls).length > 0;
        const hasChanges = JSON.stringify(pendingCalls) !== JSON.stringify(Object.fromEntries(this.pendingCalls));

        if (hasChanges) {
            this.pendingCalls.clear();
            Object.entries(pendingCalls).forEach(([requestId, callInfo]) => {
                this.pendingCalls.set(requestId, callInfo);
            });

            this.renderPendingCalls();

            if (hasNewCalls) {
                this.showContainer();
                this.showNotification('New tool execution request requires your approval', 'info');
            } else {
                this.hideContainer();
            }
        }
    }

    renderPendingCalls() {
        const listContainer = document.getElementById('tool-call-list');
        if (!listContainer) return;

        if (this.pendingCalls.size === 0) {
            listContainer.innerHTML = '<div class="no-pending-calls">No pending tool calls</div>';
            return;
        }

        listContainer.innerHTML = '';

        this.pendingCalls.forEach((callInfo, requestId) => {
            const callElement = this.createToolCallElement(requestId, callInfo);
            listContainer.appendChild(callElement);
        });
    }

    createToolCallElement(requestId, callInfo) {
        const element = document.createElement('div');
        element.className = 'tool-call-item';
        element.dataset.requestId = requestId;

        const riskLevelClass = this.getRiskLevelClass(callInfo.risk_level);
        const riskIcon = this.getRiskIcon(callInfo.risk_level);

        // Format parameters for display
        const parametersList = Object.entries(callInfo.parameters)
            .slice(0, 3) // Show first 3 parameters
            .map(([key, value]) => `<li><strong>${key}:</strong> ${this.formatParameterValue(value)}</li>`)
            .join('');

        const moreParams = Object.keys(callInfo.parameters).length > 3 
            ? `<li><em>... and ${Object.keys(callInfo.parameters).length - 3} more parameters</em></li>`
            : '';

        element.innerHTML = `
            <div class="tool-call-header">
                <div class="tool-info">
                    <h4 class="tool-name">
                        <i class="fas fa-cog"></i>
                        ${callInfo.tool_name}
                    </h4>
                    <span class="risk-badge ${riskLevelClass}">
                        ${riskIcon} ${callInfo.risk_level.toUpperCase()}
                    </span>
                </div>
                <div class="tool-actions">
                    <button class="btn-approve" onclick="toolCallManager.approveCall('${requestId}')">
                        <i class="fas fa-check"></i> Approve
                    </button>
                    <button class="btn-reject" onclick="toolCallManager.rejectCall('${requestId}')">
                        <i class="fas fa-times"></i> Reject
                    </button>
                </div>
            </div>
            <div class="tool-details">
                <div class="confirmation-message">
                    ${this.formatConfirmationMessage(callInfo.confirmation_message)}
                </div>
                <div class="parameters-section">
                    <h5><i class="fas fa-list"></i> Parameters:</h5>
                    <ul class="parameters-list">
                        ${parametersList}
                        ${moreParams}
                    </ul>
                </div>
                <div class="tool-metadata">
                    <small class="request-id">Request ID: ${requestId}</small>
                    <small class="created-time">Created: ${callInfo.created_at ? new Date(callInfo.created_at).toLocaleTimeString() : 'Unknown'}</small>
                </div>
            </div>
        `;

        return element;
    }

    getRiskLevelClass(riskLevel) {
        const classes = {
            'safe': 'risk-safe',
            'moderate': 'risk-moderate',
            'high': 'risk-high'
        };
        return classes[riskLevel] || 'risk-unknown';
    }

    getRiskIcon(riskLevel) {
        const icons = {
            'safe': '✅',
            'moderate': '⚠️',
            'high': '🚨'
        };
        return icons[riskLevel] || '❓';
    }

    formatParameterValue(value) {
        if (typeof value === 'string' && value.length > 50) {
            return value.substring(0, 50) + '...';
        }
        if (typeof value === 'object') {
            return JSON.stringify(value).substring(0, 50) + '...';
        }
        return String(value);
    }

    formatConfirmationMessage(message) {
        if (!message) return '';
        
        // Convert markdown-style formatting to HTML
        return message
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
    }

    async approveCall(requestId) {
        try {
            const response = await fetch(`/api/tool-calls/${requestId}/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: this.sessionId
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('Tool call approved and executed successfully', 'success');
                this.removePendingCall(requestId);
                
                // Add the result to chat if available
                if (data.result && data.result.result) {
                    addMessage(data.result.result, false);
                }
            } else {
                this.showNotification(`Failed to approve tool call: ${data.error || 'Unknown error'}`, 'error');
            }
        } catch (error) {
            console.error('Error approving tool call:', error);
            this.showNotification('Error approving tool call', 'error');
        }
    }

    async rejectCall(requestId, reason = 'User rejected') {
        try {
            const response = await fetch(`/api/tool-calls/${requestId}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    reason: reason
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('Tool call rejected', 'info');
                this.removePendingCall(requestId);
                
                // Add rejection message to chat
                addMessage(`Tool execution was rejected: ${reason}`, false);
            } else {
                this.showNotification(`Failed to reject tool call: ${data.error || 'Unknown error'}`, 'error');
            }
        } catch (error) {
            console.error('Error rejecting tool call:', error);
            this.showNotification('Error rejecting tool call', 'error');
        }
    }

    removePendingCall(requestId) {
        this.pendingCalls.delete(requestId);
        this.renderPendingCalls();
        
        if (this.pendingCalls.size === 0) {
            this.hideContainer();
        }
    }

    showContainer() {
        const container = document.getElementById('tool-call-container');
        if (container) {
            container.classList.add('visible');
        }
    }

    hideContainer() {
        const container = document.getElementById('tool-call-container');
        if (container) {
            container.classList.remove('visible');
        }
    }

    showNotification(message, type = 'info') {
        // Use existing notification system if available
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    destroy() {
        this.stopPolling();
        const container = document.getElementById('tool-call-container');
        if (container) {
            container.remove();
        }
    }
}

// Global instance
let toolCallManager = null;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    toolCallManager = new ToolCallManager();
    
    // Update session ID when it changes
    if (typeof sessionId !== 'undefined') {
        toolCallManager.setSessionId(sessionId);
    }
});

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    if (toolCallManager) {
        toolCallManager.destroy();
    }
});
