"""
Main entry point for the EnrollPredict.ai application.
"""
import uvicorn
import logging
from pathlib import Path
from src.app import app
from src.config import DEBUG, HOST, PORT

# Configure logging
logging.basicConfig(
    level=logging.DEBUG if DEBUG else logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# Create necessary directories if they don't exist
BASE_DIR = Path(__file__).resolve().parent
STATIC_DIR = BASE_DIR / "static"
TEMPLATES_DIR = BASE_DIR / "templates"
MODEL_DIR = BASE_DIR / "src" / "model"
PREDICT_MODEL_DIR = BASE_DIR / "src" / "predict_model"

STATIC_DIR.mkdir(exist_ok=True)
TEMPLATES_DIR.mkdir(exist_ok=True)
MODEL_DIR.mkdir(exist_ok=True)
PREDICT_MODEL_DIR.mkdir(exist_ok=True)

if __name__ == "__main__":
    logger.info(f"Starting EnrollPredict.ai server on {HOST}:{PORT}")
    logger.info(f"Debug mode: {DEBUG}")
    
    uvicorn.run(
        "src.app:app",
        host=HOST,
        port=PORT,
        reload=DEBUG,
        log_level="debug" if DEBUG else "info",
        access_log=True
    )
